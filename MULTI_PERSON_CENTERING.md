# Multi-Person Centering Implementation

## Overview

The multi-person centering feature has been successfully implemented to support centering on up to 10 people in a single image. The system now intelligently selects the most "foremost" (prominent) people and centers the image based on their weighted centroid.

## Key Features

### 1. Configurable Person Limit
- **Range**: 1-10 people
- **Configuration**: `centering.max_people_for_centering` in `config.yaml`
- **Default**: 2 people (maintains backward compatibility)

### 2. Intelligent Person Selection
The system uses a sophisticated scoring algorithm to identify the most "foremost" people:

- **Size Score (50% weight)**: Larger bounding boxes indicate people who are closer/more prominent
- **Confidence Score (30% weight)**: Higher detection confidence indicates better quality detections
- **Position Score (20% weight)**: People closer to the image center are preferred for group photos

### 3. Weighted Centroid Calculation
Once the most foremost people are selected, the system:
- Calculates individual person centers using the configured centering method (face_chest_based, keypoint_based, etc.)
- Weights each person's center by their prominence (size × confidence)
- Computes a weighted centroid as the final centering target

### 4. Backward Compatibility
- Legacy `auto_two_person_centering` settings are still supported
- New `auto_multi_person_centering` setting takes precedence when both are present
- Existing configurations continue to work without modification

## Configuration

### GUI Configuration (Recommended)

The multi-person centering settings are now available at the **top** of the Settings panel in the GUI:

1. **Max People for Centering**: Spinner control (1-10) to set the maximum number of people to center on
2. **Auto Multi-Person Centering**: Checkbox to enable/disable automatic multi-person centering
3. **Multi-Person Status**: Live status display showing current configuration

The GUI controls automatically:
- Validate the range (1-10 people)
- Update the configuration in real-time
- Refresh the status display
- Reinitialize the photo centerer with new settings

### Configuration File Settings

```yaml
centering:
  # Multi-person centering settings (configurable in GUI)
  max_people_for_centering: 2  # Maximum number of people to center on (1-10)
                               # Most prominent/foremost people up to this limit will be used for centering
                               # People beyond this limit may be cropped out
  auto_multi_person_centering: true  # Enable automatic multi-person centering when multiple people detected

  # Legacy settings (still supported for backward compatibility)
  auto_two_person_centering: true  # DEPRECATED: Use auto_multi_person_centering
  min_people_for_two_person: 2     # DEPRECATED: Use max_people_for_centering
  max_people_for_two_person: 3     # DEPRECATED: Use max_people_for_centering
```

### Configuration Examples

#### Center on single person only
```yaml
centering:
  max_people_for_centering: 1
  auto_multi_person_centering: true
```

#### Center on up to 3 most prominent people
```yaml
centering:
  max_people_for_centering: 3
  auto_multi_person_centering: true
```

#### Center on up to 5 most prominent people (good for group photos)
```yaml
centering:
  max_people_for_centering: 5
  auto_multi_person_centering: true
```

## How It Works

### 1. Detection Phase
- The system detects all people in the image using YOLO or OpenPose
- Each detection includes bounding box, confidence, center point, and keypoints

### 2. Selection Phase
- If detections ≤ `max_people_for_centering`: Use all detections
- If detections > `max_people_for_centering`: Select the most foremost people
- Selection is based on prominence score (size + confidence + position)

### 3. Centering Phase
- Calculate individual centers for selected people using configured method
- Compute weighted centroid based on person prominence
- Apply centering using the centroid as the target point

### 4. Cropping Phase
- People beyond the limit may be cropped out during centering
- The system prioritizes keeping the selected foremost people in frame

## Implementation Details

### Files Modified

1. **`config.yaml`**: Added new multi-person settings with detailed comments
2. **`src/photo_center/utils/config.py`**: Added configuration properties with validation
3. **`src/photo_center/image_processing/centering.py`**: Implemented multi-person logic
4. **`src/photo_center/image_processing/crop_centering.py`**: Implemented crop-based multi-person logic
5. **`src/photo_center/ui/main_window.py`**: Added GUI controls at the top of settings panel

### Key Methods Added

- `_get_most_foremost_people()`: Selects most prominent people
- `_center_multiple_people_internal()`: Handles multi-person centering logic
- `max_people_for_centering` property: Configuration accessor with validation
- `auto_multi_person_centering` property: Configuration accessor with legacy fallback

### GUI Components Added

- **Max People Spinner**: `QSpinBox` with range 1-10 and tooltip
- **Auto Multi-Person Checkbox**: `QCheckBox` for enabling/disabling feature
- **Status Display**: Live status label showing current configuration
- **Update Methods**: Real-time configuration updates and photo centerer reinitialization

## Usage Examples

### Command Line
```bash
# Process with default settings (2 people max)
uv run photo-center -i image.jpg

# Process batch with custom config
uv run photo-center -i /path/to/images --batch
```

### Programmatic Usage
```python
from photo_center.utils.config import Config
from photo_center.image_processing.centering import PhotoCenterer

# Configure for up to 4 people
config = Config()
config.set('centering.max_people_for_centering', 4)
config.set('centering.auto_multi_person_centering', True)

# Create centerer and process image
centerer = PhotoCenterer(config)
result = centerer.center_image(image, detections)
```

## Testing

A comprehensive test suite has been created (`test_multi_person_centering.py`) that verifies:
- Configuration property validation and range clamping
- Foremost people selection algorithm
- Multi-person centering with various configurations
- Backward compatibility with legacy settings

Run tests with:
```bash
uv run python test_multi_person_centering.py
```

## Benefits

1. **Flexible Group Handling**: Supports any group size from 1-10 people
2. **Intelligent Selection**: Automatically identifies the most important people
3. **Quality Centering**: Uses weighted centroids for better group composition
4. **Backward Compatible**: Existing configurations continue to work
5. **Configurable**: Easy to adjust for different use cases (portraits vs. group photos)

## Migration Guide

### From Legacy Two-Person Settings
```yaml
# Old configuration
centering:
  auto_two_person_centering: true
  min_people_for_two_person: 2
  max_people_for_two_person: 3

# New equivalent configuration
centering:
  auto_multi_person_centering: true
  max_people_for_centering: 3  # Use the max from legacy settings
```

### Recommended Settings by Use Case

**Portrait Photography (1-2 people)**:
```yaml
centering:
  max_people_for_centering: 2
```

**Small Group Photos (3-5 people)**:
```yaml
centering:
  max_people_for_centering: 4
```

**Large Group Photos (6+ people)**:
```yaml
centering:
  max_people_for_centering: 6
```
