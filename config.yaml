# Photo Center Configuration

# Model settings
models:
  # Model selection: yolo, openpose, auto
  detection_model: "auto"  # Auto-select best model for each image

  human_detection:
    model_path: "yolo11x-pose.pt"  # Latest YOLO11 extra-large model for best accuracy
    confidence_threshold: 0.4  # Lowered for better face detection
    device: "cuda"  # Using GPU with 24GB VRAM
    face_detection_priority: true  # Prioritize face/chest keypoints for centering

  openpose:
    confidence_threshold: 0.5  # Tracking confidence threshold
    detection_confidence: 0.5  # Detection confidence threshold
    visibility_threshold: 0.5  # Minimum keypoint visibility to include
    graduation_photo_mode: true  # Optimize for graduation photos (center-person selection)
  
# Image processing settings
image_processing:
  output_format: "tiff"  # jpg, png, tiff (tiff recommended for 16-bit support)
  output_quality: 95
  preserve_raw: true
  preserve_bit_depth: true  # Preserve 16-bit when possible
  force_8bit_for_jpeg: true  # Always convert to 8-bit for JPEG output
  max_output_size: [3000, 3000]  # [width, height] or null for original size

  # RAW processing settings
  raw_processing:
    brightness: 1.0  # Brightness multiplier (1.0 = no change when auto_bright is enabled)
    highlight_recovery: 0  # Highlight recovery (0-9, 0 = clip highlights)
    exposure_shift: 0.0  # Exposure shift in stops (-3.0 to +3.0)
    use_camera_wb: true  # Use camera white balance
    gamma_curve: [2.222, 4.5]  # Gamma curve [gamma, toe_slope] for sRGB
    no_auto_bright: false  # Enable rawpy auto brightness for consistent results
  
# Orientation detection settings (always enabled, no user configuration needed)
# Orientation detection automatically processes first and corrects subject orientation
orientation_detection:
  method: "head_body_vector"  # head_body_vector, shoulder_alignment, combined
  confidence_threshold: 0.7  # Minimum confidence to apply rotation
  head_body_angle_threshold: 45  # Degrees - if head-body vector deviates more than this, consider rotation
  shoulder_angle_threshold: 30  # Degrees - if shoulders are tilted more than this, consider rotation
  min_keypoints_required: 4  # Minimum keypoints needed for reliable orientation detection
  rotation_angles: [90, 180, 270]  # Allowed rotation angles in degrees
  prefer_minimal_rotation: true  # Prefer smaller rotation angles when multiple options exist

# Centering algorithm settings
centering:
  method: "face_chest_based"  # face_chest_based, keypoint_based, bbox_based, center_of_mass
  target_position: [0.5, 0.25]  # [x, y] as fraction of image (optimized for waist-up framing)
  margin_ratio: 0.15  # Increased margin for better face framing
  crop_aspect_ratio: null  # null for original ratio, or [16, 9] for specific ratio
  face_weight: 0.5  # Weight for face keypoints in centering calculation
  chest_weight: 0.3  # Weight for chest/shoulder keypoints
  hip_weight: 0.2  # Weight for hip keypoints in centering calculation
  use_crop_centering: true  # true for aggressive cropping (perfect centering), false for padding-based centering

  # Multi-person centering settings (configurable in GUI)
  max_people_for_centering: 2  # Maximum number of people to center on (1-10)
                               # Most prominent/foremost people up to this limit will be used for centering
                               # People beyond this limit may be cropped out
  auto_multi_person_centering: true  # Enable automatic multi-person centering when multiple people detected

  # DEPRECATED: Legacy two-person centering settings (replaced by max_people_for_centering)
  auto_two_person_centering: true  # DEPRECATED: Use auto_multi_person_centering instead
  min_people_for_two_person: 2  # DEPRECATED: Use max_people_for_centering instead
  max_people_for_two_person: 3  # DEPRECATED: Use max_people_for_centering instead
  center_on_two_people: false  # DEPRECATED: Use auto_multi_person_centering instead
  
# Reference matching settings
reference_matching:
  enabled: false
  similarity_threshold: 0.8
  keypoint_weights:
    nose: 1.0
    eyes: 0.8
    shoulders: 0.6
    
# Batch processing settings
batch:
  supported_extensions: [".cr2", ".nef", ".arw", ".dng", ".raw", ".jpg", ".jpeg", ".png", ".tiff"]
  output_suffix: "_centered"
  create_subdirectory: true
  subdirectory_name: "centered"
  max_workers: 4  # Number of parallel processes
  
# UI settings
ui:
  preview_size: [800, 600]
  show_keypoints: true
  show_bounding_box: true
  auto_refresh: true
  
# Logging settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "photo_center.log"
  max_size_mb: 10
  backup_count: 3
